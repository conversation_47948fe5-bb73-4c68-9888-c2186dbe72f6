
import requests
import json

# # 配置信息
DASHSCOPE_API_KEY = 'sk-288a3100dca24957afb251eb095fbb48'
APP_ID = 'ac5ab1e2759d432eb6c1f69493962e6e'  # 替换为实际的应用 ID
API_URL = f'https://dashscope.aliyuncs.com/api/v1/apps/{APP_ID}/completion'

def call_dashscope_api(prompt, biz_params=None, debug=None):

    headers = {
        'Authorization': f'Bearer {DASHSCOPE_API_KEY}',
        'Content-Type': 'application/json'
    }

    data = {
        'input': {
            'prompt': prompt,
            'biz_params': biz_params
        },
        'parameters': {},
        'debug': {}
    }

    try:
        response = requests.post(API_URL, headers=headers, json=data)
        response.raise_for_status()  # 检查HTTP错误
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'请求错误: {e}')
        return None
    except json.JSONDecodeError as e:
        print(f'JSON解析错误: {e}')
        return None

# 调用示例
if __name__ == '__main__':
    prompt = '去哪可以办理出国护照'

    biz_params={
        "sessionid": "123721381273821382",
        "areaname": "",
        "istest": 0,
        "areacode": ""
    }

result = call_dashscope_api(prompt, biz_params)
if result:
    # 检查响应状态
    if 'output' in result:
        print('响应成功:')
        print(result['output'].get('text', ''))
    else:
        print('API调用失败:')
        print(f"错误信息: {result}")
else:
    print('API调用失败')

import os
from http import HTTPStatus
from dashscope import Application
# biz_params = {
#     "sessionid": "123721381273821382",
#     "areaname": "",
#     "istest": 0,
#     "areacode":""
# }
# responses = Application.call(
#     # 若没有配置环境变量，可用百炼API Key将下行替换为：api_key="sk-xxx"。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
#     api_key=DASHSCOPE_API_KEY,
#     app_id=APP_ID,# 替换为实际的应用 ID
#     prompt='去哪可以办理出国护照？',
#     stream=True, # 开启流式输出
#     flow_stream_mode="message_format",# 消息模式，输出/结束节点的流式结果
#     biz_params=biz_params
#     )
#
# for response in responses:
#     if response.status_code != HTTPStatus.OK:
#         print(f'request_id={response.request_id}')
#         print(f'code={response.status_code}')
#         print(f'message={response.message}')
#         print(f'请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code')
#     else:
#         if response.output.finish_reason != "stop":
#             print(f'workflowMessage={response.output.workflow_message}\n')
#         else:
#             print(f'text={response.output.text}\n')