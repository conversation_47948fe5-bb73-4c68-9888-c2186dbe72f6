
import requests
import json

# # 配置信息
DASHSCOPE_API_KEY = 'sk-288a3100dca24957afb251eb095fbb48'
APP_ID = 'ac5ab1e2759d432eb6c1f69493962e6e'  # 替换为实际的应用 ID
API_URL = f'https://dashscope.aliyuncs.com/api/v1/apps/{APP_ID}/completion'

def call_dashscope_api_stream(prompt, biz_params=None, debug=None):
    """
    流式调用DashScope API
    """
    headers = {
        'Authorization': f'Bearer {DASHSCOPE_API_KEY}',
        'Content-Type': 'application/json',
        'X-DashScope-SSE': 'enable'  # 启用流式响应
    }

    data = {
        'input': {
            'prompt': prompt,
            'biz_params': biz_params or {}
        },
        'parameters': {
            'incremental_output': True,
        },
        'debug': debug or {}
    }

    try:
        response = requests.post(API_URL, headers=headers, json=data, stream=True)
        response.raise_for_status()  # 检查HTTP错误

        # 处理流式响应
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                print(line)
                # SSE格式通常以"data: "开头
                if line.startswith('data: '):
                    data_str = line[6:]  # 去掉"data: "前缀
                    if data_str.strip() == '[DONE]':
                        break
                    try:
                        data_json = json.loads(data_str)
                        yield data_json
                    except json.JSONDecodeError:
                        continue

    except requests.exceptions.RequestException as e:
        print(f'请求错误: {e}')
        return None

# 调用示例
if __name__ == '__main__':
    prompt = '去哪可以办理出国护照'

    biz_params = {
        "sessionid": "32987482374892378432",
        "areaname": "",
        "istest": 0,
        "areacode": ""
    }

    print('开始流式调用API...')
    try:
        for chunk in call_dashscope_api_stream(prompt, biz_params):
            if chunk:
                # 处理流式响应数据
                print('收到数据块:', json.dumps(chunk, ensure_ascii=False, indent=2))

                # 如果有输出文本，实时显示
                if 'output' in chunk and 'text' in chunk['output']:
                    print(f'输出文本: {chunk["output"]["text"]}')

    except Exception as e:
        print(f'流式调用出错: {e}')

